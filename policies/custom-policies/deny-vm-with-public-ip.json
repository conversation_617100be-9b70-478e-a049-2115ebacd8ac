{"properties": {"displayName": "Deny VM creation with Public IP in template", "policyType": "Custom", "mode": "All", "description": "Prevent creation of Virtual Machines that include Public IP addresses in ARM templates", "metadata": {"version": "1.0.0", "category": "Compute"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/deployments"}, {"anyOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Compute/virtualMachines"}, {"field": "Microsoft.Resources/deployments/template.resources[*].resources[*].type", "equals": "Microsoft.Compute/virtualMachines"}]}, {"anyOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Network/publicIPAddresses"}, {"field": "Microsoft.Resources/deployments/template.resources[*].resources[*].type", "equals": "Microsoft.Network/publicIPAddresses"}]}]}, "then": {"effect": "[parameters('effect')]"}}}}