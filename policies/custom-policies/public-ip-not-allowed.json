{"properties": {"displayName": "Deny assignment of Public IP to VM NICs", "policyType": "Custom", "mode": "Indexed", "description": "Prevent Public IP assignment directly to Virtual Machine NIC", "metadata": {"version": "1.0.0", "category": "Network"}, "parameters": {}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Network/networkInterfaces/ipconfigurations[*].publicIpAddress.id", "exists": "true"}]}, "then": {"effect": "deny"}}}}