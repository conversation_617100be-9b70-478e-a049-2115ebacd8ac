{"properties": {"displayName": "Deny assignment of Public IP to VM NICs", "policyType": "Custom", "mode": "All", "description": "Prevent Public IP assignment directly to Virtual Machine Network Interfaces and block VM deployments with Public IPs", "metadata": {"version": "1.0.0", "category": "Network"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Network/networkInterfaces/ipconfigurations[*].publicIpAddress.id", "exists": "true"}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/deployments"}, {"anyOf": [{"allOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Compute/virtualMachines"}, {"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Network/publicIPAddresses"}]}, {"allOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Resources/deployments/template.resources[*].properties.ipConfigurations[*].properties.publicIPAddress", "exists": "true"}]}]}]}]}, "then": {"effect": "[parameters('effect')]"}}}}