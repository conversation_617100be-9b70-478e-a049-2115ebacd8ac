{"properties": {"displayName": "Deny Public IP assignment to Virtual Machines", "policyType": "Custom", "mode": "All", "description": "Comprehensive policy to prevent Public IP assignment to Virtual Machines through Network Interfaces and ARM template deployments", "metadata": {"version": "1.0.0", "category": "Network"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Network/networkInterfaces/ipconfigurations[*].publicIpAddress.id", "exists": "true"}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/deployments"}, {"anyOf": [{"allOf": [{"anyOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Compute/virtualMachines"}, {"field": "Microsoft.Resources/deployments/template.resources[*].resources[*].type", "equals": "Microsoft.Compute/virtualMachines"}]}, {"anyOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Network/publicIPAddresses"}, {"field": "Microsoft.Resources/deployments/template.resources[*].resources[*].type", "equals": "Microsoft.Network/publicIPAddresses"}]}]}, {"allOf": [{"anyOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Resources/deployments/template.resources[*].resources[*].type", "equals": "Microsoft.Network/networkInterfaces"}]}, {"anyOf": [{"field": "Microsoft.Resources/deployments/template.resources[*].properties.ipConfigurations[*].properties.publicIPAddress", "exists": "true"}, {"field": "Microsoft.Resources/deployments/template.resources[*].resources[*].properties.ipConfigurations[*].properties.publicIPAddress", "exists": "true"}]}]}]}]}]}, "then": {"effect": "[parameters('effect')]"}}}}