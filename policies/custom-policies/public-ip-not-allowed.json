{"properties": {"displayName": "Deny Public IP assignment to Virtual Machines", "policyType": "Custom", "mode": "Indexed", "description": "Prevent Public IP assignment to Virtual Machine Network Interfaces", "metadata": {"version": "1.0.0", "category": "Network"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Network/networkInterfaces/ipconfigurations[*].publicIpAddress.id", "exists": "true"}]}, "then": {"effect": "[parameters('effect')]"}}}}